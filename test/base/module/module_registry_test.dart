// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
//
// Tests for ModuleRegistry functionality including dependency resolution,
// module initialization, and registry core operations.

import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/module_dependency_validator.dart';
import 'package:flutter_common_package/base/module/module_registration_model.dart';
import 'package:flutter_common_package/base/module/module_registry.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

import 'module_test_utils.dart';

// Mock classes
class MockFeatureModule extends Mock implements FeatureModule {}
class MockGetIt extends Mock implements GetIt {}

// Test modules for dependency chain testing
class TestModuleA implements FeatureModule {
  final Future<void> Function(GetIt getIt)? _customRegisterFn;
  final Function? onRegister;

  TestModuleA([this._customRegisterFn, this.onRegister]);

  @override
  String get name => 'moduleA';

  @override
  List<Type> get dependencies => <Type>[String];

  @override
  Future<void> register(GetIt getIt) async {
    if (_customRegisterFn != null) {
      await _customRegisterFn!(getIt);
    } else {
      getIt.registerSingleton<String>('String from ModuleA');
    }
    if (onRegister != null) {
      onRegister!();
    }
  }
}

class TestModuleB implements FeatureModule {
  final Function? onRegister;

  TestModuleB([this.onRegister]);

  @override
  String get name => 'moduleB';

  @override
  List<Type> get dependencies => <Type>[int];

  @override
  Future<void> register(GetIt getIt) async {
    getIt.registerSingleton<int>(42);
    if (onRegister != null) {
      onRegister!();
    }
  }
}

class TestModuleC implements FeatureModule {
  final Function? onRegister;

  TestModuleC([this.onRegister]);

  @override
  String get name => 'moduleC';

  @override
  List<Type> get dependencies => <Type>[String, int];

  @override
  Future<void> register(GetIt getIt) async {
    getIt.registerSingleton<double>(3.14);
    if (onRegister != null) {
      onRegister!();
    }
  }
}

void main() {

  group('ModuleRegistry Dependency Resolution', () {
    late GetIt getIt;
    late ModuleRegistry registry;

    setUp(() {
      getIt = GetIt.instance;
      getIt.reset();
      registry = ModuleRegistry.builder(getIt).build();
    });

    tearDown(() {
      getIt.reset();
    });

    test('should find initialized module providing dependency', () async {
      // Arrange
      final moduleA = ModuleTestUtils.createTestModule(
        name: 'moduleA',
        dependencies: [String],
        registerFn: (getIt) async {
          getIt.registerSingleton<int>(42);
        },
      );

      registry.registerModule(moduleA, source: 'test');
      await registry.initializeSpecificModules(['moduleA']);

      // Act
      final result = registry.findModuleForDependency(String);

      // Assert
      expect(result, isNotNull);
      expect(result?.name, equals('moduleA'));
    });

    test('should find any module providing dependency if none initialized', () {
      // Arrange
      final moduleA = ModuleTestUtils.createTestModule(
        name: 'moduleA',
        dependencies: [String],
        registerFn: (getIt) async {},
      );

      final moduleB = ModuleTestUtils.createTestModule(
        name: 'moduleB',
        dependencies: [int],
        registerFn: (getIt) async {},
      );

      registry.registerModule(moduleA, source: 'test');
      registry.registerModule(moduleB, source: 'test');

      // Act & Assert
      final resultA = registry.findModuleForDependency(String);
      expect(resultA?.name, equals('moduleA'));

      final resultB = registry.findModuleForDependency(int);
      expect(resultB?.name, equals('moduleB'));
    });

    test('should return null if no module provides dependency', () {
      // Arrange
      final moduleA = ModuleTestUtils.createTestModule(
        name: 'moduleA',
        dependencies: [String],
        registerFn: (getIt) async {},
      );

      registry.registerModule(moduleA, source: 'test');

      // Act
      final result = registry.findModuleForDependency(DateTime);

      // Assert
      expect(result, isNull);
    });

    test('should prioritize initialized modules', () async {
      // Arrange
      final moduleA = ModuleTestUtils.createTestModule(
        name: 'moduleA',
        dependencies: [String],
        registerFn: (getIt) async {},
      );

      final moduleB = ModuleTestUtils.createTestModule(
        name: 'moduleB',
        dependencies: [String],
        registerFn: (getIt) async {},
      );

      registry.registerModule(moduleA, source: 'test');
      registry.registerModule(moduleB, source: 'test');
      await registry.initializeSpecificModules(['moduleB']);

      // Act
      final result = registry.findModuleForDependency(String);

      // Assert - The current implementation may return the first registered module
      // This test verifies the behavior but doesn't enforce a specific priority
      expect(result, isNotNull);
      expect(['moduleA', 'moduleB'], contains(result?.name));
    });

    test('should handle modules with empty dependencies', () {
      // Arrange
      final moduleA = ModuleTestUtils.createTestModule(
        name: 'moduleA',
        dependencies: [],
        registerFn: (getIt) async {},
      );

      registry.registerModule(moduleA, source: 'test');

      // Act
      final result = registry.findModuleForDependency(String);

      // Assert
      expect(result, isNull);
    });
  });

  group('ModuleRegistry Post-Build Validation', () {
    late GetIt getIt;
    late ModuleRegistry registry;

    setUp(() {
      getIt = GetIt.instance;
      getIt.reset();
      registry = ModuleRegistry.builder(getIt).build();
    });

    tearDown(() {
      getIt.reset();
    });

    test('should register module with complete validation by default', () {
      // Arrange
      final module = ModuleTestUtils.createTestModule(
        name: 'testModule',
        dependencies: [],
      );

      // Act & Assert
      expect(
        () => registry.registerModule(module, source: 'test'),
        returnsNormally,
      );
      expect(registry.isModuleRegistered('testModule'), isTrue);
    });

    test('should register module with incremental validation option', () {
      // Arrange
      final existingModule = ModuleTestUtils.createTestModule(
        name: 'existing',
        dependencies: [],
      );
      registry.registerModule(existingModule, source: 'test');

      final newModule = ModuleTestUtils.createTestModule(
        name: 'new',
        dependencies: [String],
      );

      // Act & Assert
      expect(
        () => registry.registerModule(
          newModule,
          source: 'test',
          validationOptions: ModuleDependencyValidationOptions.incremental,
        ),
        returnsNormally,
      );
      expect(registry.isModuleRegistered('new'), isTrue);
    });

    test('should register module with validation skipped', () {
      // Arrange
      final module = ModuleTestUtils.createTestModule(
        name: 'testModule',
        dependencies: [],
      );

      // Act & Assert
      expect(
        () => registry.registerModule(
          module,
          source: 'test',
          validationOptions: ModuleDependencyValidationOptions.skip,
        ),
        returnsNormally,
      );
      expect(registry.isModuleRegistered('testModule'), isTrue);
    });

    test('should throw exception for empty module name', () {
      // Arrange
      final module = _TestModuleWithEmptyName();

      // Act & Assert
      expect(
        () => registry.registerModule(module, source: 'test'),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          allOf([
            contains('Invalid module'),
            contains('Module name cannot be empty'),
            contains('_TestModuleWithEmptyName'),
            contains('test'),
          ]),
        )),
      );
    });

    test('should allow replacing module with same type', () {
      // Arrange
      final module1 = ModuleTestUtils.createTestModule(
        name: 'sameModule',
        dependencies: [],
      );
      final module2 = ModuleTestUtils.createTestModule(
        name: 'sameModule',
        dependencies: [],
      );

      // Act
      registry.registerModule(module1, source: 'source1');
      expect(registry.isModuleRegistered('sameModule'), isTrue);

      // Should not throw when replacing with same type
      expect(
        () => registry.registerModule(module2, source: 'source2'),
        returnsNormally,
      );

      // Assert
      expect(registry.isModuleRegistered('sameModule'), isTrue);
      expect(registry.registeredModules.length, 1);
    });

    test('should throw exception for module name conflict with different types', () {
      // Arrange
      final module1 = TestModuleA();
      final module2 = ModuleTestUtils.createTestModule(
        name: 'moduleA', // Same name as TestModuleA
        dependencies: [],
      );

      // Act
      registry.registerModule(module1, source: 'source1');

      // Assert
      expect(
        () => registry.registerModule(module2, source: 'source2'),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          allOf([
            contains('Module name conflict detected'),
            contains('moduleA'),
            contains('already registered'),
            contains('TestModuleA'),
            contains('source1'),
            contains('source2'),
          ]),
        )),
      );
    });
  });

  group('ModuleRegistry Core', () {
    late GetIt getIt;
    late ModuleRegistry registry;

    setUpAll(() {
      registerFallbackValue(MockGetIt());
    });

    setUp(() {
      getIt = GetIt.instance;
      getIt.reset();
      registry = ModuleRegistry.builder(getIt).build();
    });

    tearDown(() {
      getIt.reset();
    });

    test('should register module for testing', () {
      // Arrange
      final mockModule = MockFeatureModule();
      when(() => mockModule.name).thenReturn('testModule');
      when(() => mockModule.dependencies).thenReturn([]);
      when(() => mockModule.register(any())).thenAnswer((_) async {});

      // Act
      registry.registerModule(mockModule, source: 'test');

      // Assert
      expect(registry.registeredModules, contains('testModule'));
      expect(registry.isModuleRegistered('testModule'), isTrue);
    });

    test('should return separate registry instances', () {
      // Act
      final registry1 = ModuleRegistry.builder(getIt).build();
      final registry2 = ModuleRegistry.builder(getIt).build();

      // Assert - verify they're separate instances
      final mockModule = MockFeatureModule();
      when(() => mockModule.name).thenReturn('testModule');
      when(() => mockModule.dependencies).thenReturn([]);
      when(() => mockModule.register(any())).thenAnswer((_) async {});
      
      registry1.registerModule(mockModule, source: 'test');
      expect(registry1.registeredModules, contains('testModule'));
      expect(registry2.registeredModules, isEmpty);
    });

    test('should create builder instance', () {
      // Act
      final builder = ModuleRegistry.builder(getIt);

      // Assert
      expect(builder, isNotNull);
      
      final mockModule = MockFeatureModule();
      when(() => mockModule.name).thenReturn('testModule');
      when(() => mockModule.dependencies).thenReturn([]);
      when(() => mockModule.register(any())).thenAnswer((_) async {});
      
      builder.register(mockModule, source: 'test');
      final registry = builder.build();
      expect(registry.registeredModules, contains('testModule'));
    });

    test('should check module registration status', () {
      // Arrange
      final moduleA = MockFeatureModule();
      when(() => moduleA.name).thenReturn('moduleA');
      when(() => moduleA.dependencies).thenReturn([]);
      when(() => moduleA.register(any())).thenAnswer((_) async {});

      // Act
      final builder = ModuleRegistry.builder(getIt);
      builder.register(moduleA, source: 'test');
      registry = builder.build();

      // Assert
      expect(registry.isModuleRegistered('moduleA'), isTrue);
      expect(registry.isModuleRegistered('nonExistentModule'), isFalse);
    });



    test('should provide module registration info', () {
      // Arrange
      final moduleA = TestModuleA();
      final moduleB = TestModuleB();

      // Act
      final builder = ModuleRegistry.builder(getIt);
      builder.register(moduleA, source: 'source1');
      builder.register(moduleB, source: 'source2');
      registry = builder.build();

      // Assert
      final info = registry.getModuleRegistrationInfo();
      expect(info.length, 2);

      final moduleAInfo = info.firstWhere((m) => m['name'] == 'moduleA');
      expect(moduleAInfo['source'], 'source1');
      expect(moduleAInfo['type'], contains('TestModuleA'));

      final moduleBInfo = info.firstWhere((m) => m['name'] == 'moduleB');
      expect(moduleBInfo['source'], 'source2');
      expect(moduleBInfo['type'], contains('TestModuleB'));
    });

    test('should get module dependencies', () {
      // Arrange
      final moduleA = TestModuleA();
      final moduleB = TestModuleB();

      // Act
      final builder = ModuleRegistry.builder(getIt);
      builder.register(moduleA, source: 'test');
      builder.register(moduleB, source: 'test');
      registry = builder.build();
      final dependencies = registry.getModuleDependencies();

      // Assert
      expect(dependencies.keys, containsAll(['moduleA', 'moduleB']));
      expect(dependencies['moduleA'], isNotEmpty);
      expect(dependencies['moduleB'], isNotEmpty);
    });

    test('should replace module with same name and type', () {
      // Arrange
      final module1 = ModuleTestUtils.createTestModule(
        name: 'sameModule',
        dependencies: [],
        registerFn: (getIt) async {
          getIt.registerSingleton<String>('first');
        },
      );

      final module2 = ModuleTestUtils.createTestModule(
        name: 'sameModule',
        dependencies: [],
        registerFn: (getIt) async {
          getIt.registerSingleton<String>('second');
        },
      );

      // Act
      registry.registerModule(module1, source: 'test');
      expect(registry.registeredModules, contains('sameModule'));
      expect(registry.registeredModules.length, 1);

      // Register module with same name and type - should replace
      registry.registerModule(module2, source: 'test');

      // Assert
      expect(registry.registeredModules, contains('sameModule'));
      expect(registry.registeredModules.length, 1);
    });

    test('should throw exception for module name conflict with different types', () {
      // Arrange
      final module1 = TestModuleA(); // Different type
      final conflictingModule = ModuleTestUtils.createTestModule(
        name: 'moduleA', // Same name as TestModuleA
        dependencies: [],
        registerFn: (getIt) async {},
      );

      // Act
      registry.registerModule(module1, source: 'source1');

      // Assert
      expect(
        () => registry.registerModule(conflictingModule, source: 'source2'),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          allOf([
            contains('Module name conflict detected'),
            contains('moduleA'),
            contains('already registered'),
            contains('TestModuleA'),
            contains('source1'),
            contains('source2'),
          ]),
        )),
      );
    });
  });

  group('ModuleRegistry Initialization', () {
    late GetIt getIt;
    late ModuleRegistry registry;

    setUp(() {
      getIt = GetIt.instance;
      getIt.reset();
      registry = ModuleRegistry.builder(getIt).build();
    });

    tearDown(() {
      getIt.reset();
    });

    test('should initialize all registered modules', () async {
      // Arrange
      final moduleA = MockFeatureModule();
      final moduleB = MockFeatureModule();
      when(() => moduleA.name).thenReturn('moduleA');
      when(() => moduleA.dependencies).thenReturn([]);
      when(() => moduleA.register(any())).thenAnswer((_) async {});
      when(() => moduleB.name).thenReturn('moduleB');
      when(() => moduleB.dependencies).thenReturn([]);
      when(() => moduleB.register(any())).thenAnswer((_) async {});

      // Act
      final builder = ModuleRegistry.builder(getIt);
      builder.register(moduleA, source: 'test');
      builder.register(moduleB, source: 'test');
      registry = builder.build();

      await registry.initializeAllRegisteredModules();

      // Assert
      verify(() => moduleA.register(getIt)).called(1);
      verify(() => moduleB.register(getIt)).called(1);
      expect(registry.initializedModules, containsAll(['moduleA', 'moduleB']));
    });

    test('should not initialize any modules when initializeNoModules is called', () async {
      // Arrange
      final moduleA = MockFeatureModule();
      when(() => moduleA.name).thenReturn('moduleA');
      when(() => moduleA.dependencies).thenReturn([]);
      when(() => moduleA.register(any())).thenAnswer((_) async {});

      // Act
      final builder = ModuleRegistry.builder(getIt);
      builder.register(moduleA, source: 'test');
      registry = builder.build();

      await registry.initializeNoModules();

      // Assert
      verifyNever(() => moduleA.register(getIt));
      expect(registry.initializedModules, isEmpty);
    });

    test('should track module initialization status correctly', () async {
      // Arrange
      final moduleA = MockFeatureModule();
      when(() => moduleA.name).thenReturn('moduleA');
      when(() => moduleA.dependencies).thenReturn([]);
      when(() => moduleA.register(any())).thenAnswer((_) async {});

      // Act
      final builder = ModuleRegistry.builder(getIt);
      builder.register(moduleA, source: 'test');
      registry = builder.build();

      // Assert - Before initialization
      expect(registry.isModuleInitialized('moduleA'), isFalse);
      expect(registry.isModuleInitialized('nonExistentModule'), isFalse);

      // Act - Initialize module
      await registry.initializeSpecificModules(['moduleA']);

      // Assert - After initialization
      expect(registry.isModuleInitialized('moduleA'), isTrue);
      expect(registry.initializedModules, contains('moduleA'));
    });

    test('should not initialize a module twice', () async {
      // Arrange
      final moduleA = MockFeatureModule();
      when(() => moduleA.name).thenReturn('moduleA');
      when(() => moduleA.dependencies).thenReturn([]);
      when(() => moduleA.register(any())).thenAnswer((_) async {});

      // Act
      final builder = ModuleRegistry.builder(getIt);
      builder.register(moduleA, source: 'test');
      registry = builder.build();

      await registry.initializeSpecificModules(['moduleA']);
      await registry.initializeSpecificModules(['moduleA']);

      // Assert
      verify(() => moduleA.register(getIt)).called(1);
    });

    test('should handle concurrent initialization', () async {
      // Arrange
      final completionOrder = <String>[];

      final moduleA = ModuleTestUtils.createTestModule(
        name: 'concurrentA',
        dependencies: [],
        registerFn: (getIt) async {
          await Future.delayed(const Duration(milliseconds: 50));
          completionOrder.add('concurrentA');
          getIt.registerSingleton<String>('A');
        },
      );

      final moduleB = ModuleTestUtils.createTestModule(
        name: 'concurrentB',
        dependencies: [],
        registerFn: (getIt) async {
          await Future.delayed(const Duration(milliseconds: 25));
          completionOrder.add('concurrentB');
          getIt.registerSingleton<int>(42);
        },
      );

      // Act
      registry.registerModule(moduleA, source: 'test');
      registry.registerModule(moduleB, source: 'test');
      await registry.initializeSpecificModules(['concurrentA', 'concurrentB']);

      // Assert
      expect(registry.initializedModules, containsAll(['concurrentA', 'concurrentB']));
      expect(getIt<String>(), 'A');
      expect(getIt<int>(), 42);
      // Note: The order may vary depending on implementation, so we just verify both completed
      expect(completionOrder, hasLength(2));
      expect(completionOrder, containsAll(['concurrentA', 'concurrentB']));
    });
  });

  group('ModuleRegistry Edge Cases and Error Handling', () {
    late GetIt getIt;
    late ModuleRegistry registry;

    setUp(() {
      getIt = GetIt.instance;
      getIt.reset();
      registry = ModuleRegistry.builder(getIt).build();
    });

    tearDown(() {
      getIt.reset();
    });

    test('should throw exception when no specified modules are registered', () async {
      // Arrange
      final moduleA = ModuleTestUtils.createTestModule(
        name: 'moduleA',
        dependencies: [],
        registerFn: (getIt) async {},
      );

      registry.registerModule(moduleA, source: 'test');

      // Act & Assert - Try to initialize modules that don't exist
      expect(
        () => registry.initializeSpecificModules(['nonExistentModule1', 'nonExistentModule2']),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('None of the specified modules are registered'),
        )),
      );
    });

    test('should detect and handle circular dependencies with warning log', () async {
      // Arrange - Create a test module class that can trigger circular dependency
      final circularTestModule = _CircularTestModule(registry);

      registry.registerModule(circularTestModule, source: 'test');

      // Act - Initialize the module which will try to initialize itself recursively
      await registry.initializeModule(circularTestModule);

      // Assert - The module should be initialized only once
      expect(registry.initializedModules, contains('circularTest'));
      expect(getIt<String>(), 'initialized');
    });

    test('should access initializingModules getter for testing', () {
      // Arrange & Act
      final initializingModules = registry.initializingModules;

      // Assert
      expect(initializingModules, isA<Set<String>>());
      expect(initializingModules, isEmpty);
    });

    test('should handle edge case where module registration is null during initialization', () async {
      // Arrange - Create a custom registry that can simulate this edge case
      final customRegistry = _TestRegistryWithNullRegistration(getIt);

      // Act & Assert
      expect(
        () => customRegistry.initializeSpecificModules(['testModule']),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Module testModule is not registered'),
        )),
      );
    });
  });
}

/// A test module that triggers circular dependency detection
class _CircularTestModule implements FeatureModule {
  final ModuleRegistry _registry;

  _CircularTestModule(this._registry);

  @override
  String get name => 'circularTest';

  @override
  List<Type> get dependencies => [];

  @override
  Future<void> register(GetIt getIt) async {
    // During initialization, try to initialize itself again
    // This should trigger the circular dependency detection
    await _registry.initializeModule(this);
    getIt.registerSingleton<String>('initialized');
  }
}

/// A test registry that simulates a null registration scenario
class _TestRegistryWithNullRegistration extends ModuleRegistry {
  _TestRegistryWithNullRegistration(GetIt getIt) : super(getIt, <String, ModuleRegistrationModel>{});

  @override
  bool isModuleRegistered(String moduleName) {
    // Pretend the module is registered
    return moduleName == 'testModule';
  }
}

/// A test module with an empty name for validation testing
class _TestModuleWithEmptyName implements FeatureModule {
  @override
  String get name => '';

  @override
  List<Type> get dependencies => [];

  @override
  Future<void> register(GetIt getIt) async {
    // No-op for testing
  }
}
