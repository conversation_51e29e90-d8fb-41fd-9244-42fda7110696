// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/foundation.dart';

import '../../util/extension.dart';
import 'feature_module.dart';
import 'module_registration_model.dart';

/// Options for controlling dependency validation behavior.
///
/// Provides a simple way to enable or disable validation when needed.
class ModuleDependencyValidationOptions {
  /// Whether to perform dependency validation.
  ///
  /// When true, validates the complete dependency graph for circular dependencies.
  /// When false, skips validation entirely (use with caution).
  final bool enabled;

  /// Creates validation options.
  const ModuleDependencyValidationOptions({
    this.enabled = true,
  });

  /// Default validation options that perform complete validation.
  static const ModuleDependencyValidationOptions enabled = ModuleDependencyValidationOptions();

  /// Validation options that skip all validation.
  ///
  /// Use with caution - only in scenarios where validation is guaranteed
  /// to have been performed elsewhere or in performance-critical test scenarios.
  static const ModuleDependencyValidationOptions disabled = ModuleDependencyValidationOptions(
    enabled: false,
  );
}

/// Result of a dependency validation operation.
///
/// Contains information about whether validation passed and any cycles detected.
class ModuleDependencyValidationResult {
  /// Whether the validation passed (no cycles detected).
  final bool isValid;

  /// List of detected dependency cycles, if any.
  ///
  /// Each cycle is represented as a list of module names forming the cycle.
  final List<List<String>> detectedCycles;

  /// Error message describing the validation failure, if any.
  final String? errorMessage;

  /// Creates a validation result.
  const ModuleDependencyValidationResult({
    required this.isValid,
    this.detectedCycles = const <List<String>>[],
    this.errorMessage,
  });

  /// Creates a successful validation result.
  const ModuleDependencyValidationResult.success()
      : isValid = true,
        detectedCycles = const <List<String>>[],
        errorMessage = null;

  /// Creates a failed validation result with the specified cycles and error message.
  const ModuleDependencyValidationResult.failure({
    required this.detectedCycles,
    required this.errorMessage,
  }) : isValid = false;
}

/// Utility class for validating module dependencies and detecting circular dependencies.
///
/// This class provides shared validation logic that can be used by both
/// [ModuleRegistryBuilder] and [ModuleRegistry] to ensure consistent
/// dependency validation across the module system.
///
/// The validator supports different validation modes for performance optimization:
/// - Complete validation: Validates the entire dependency graph
/// - Incremental validation: Only validates paths involving specific modules
/// - Skip validation: Bypasses validation for performance-critical scenarios
class ModuleDependencyValidator {
  /// Validates module dependencies for circular dependencies.
  ///
  /// [moduleRegistrations] - The current set of module registrations
  /// [newModule] - Optional new module being added (for incremental validation)
  /// [options] - Validation options controlling the validation behavior
  ///
  /// Returns a [ModuleDependencyValidationResult] indicating whether validation passed.
  static ModuleDependencyValidationResult validateDependencies(
    Map<String, ModuleRegistrationModel> moduleRegistrations, {
    FeatureModule? newModule,
    ModuleDependencyValidationOptions options = ModuleDependencyValidationOptions.complete,
  }) {
    // Skip validation if requested
    if (options.skipValidation) {
      return const ModuleDependencyValidationResult.success();
    }

    try {
      if (options.incrementalValidation && newModule != null) {
        return _validateIncremental(moduleRegistrations, newModule);
      } else {
        return _validateComplete(moduleRegistrations);
      }
    } catch (e) {
      'ModuleDependencyValidator'.commonLog('Validation error: $e');
      return ModuleDependencyValidationResult.failure(
        detectedCycles: const <List<String>>[],
        errorMessage: e.toString(),
      );
    }
  }

  /// Performs complete validation of all module dependencies.
  static ModuleDependencyValidationResult _validateComplete(
    Map<String, ModuleRegistrationModel> moduleRegistrations,
  ) {
    final dependencyGraph = _buildDependencyGraph(moduleRegistrations);
    return _detectCycles(dependencyGraph);
  }

  /// Performs incremental validation focusing on the new module and its dependencies.
  static ModuleDependencyValidationResult _validateIncremental(
    Map<String, ModuleRegistrationModel> moduleRegistrations,
    FeatureModule newModule,
  ) {
    // Create a temporary registration map including the new module
    final tempRegistrations = Map<String, ModuleRegistrationModel>.from(moduleRegistrations);
    tempRegistrations[newModule.name] = ModuleRegistrationModel(
      module: newModule,
      source: 'temp_validation',
      registeredAt: DateTime.now(),
    );

    // Build dependency graph and check only paths involving the new module
    final dependencyGraph = _buildDependencyGraph(tempRegistrations);
    return _detectCyclesInvolvingModule(dependencyGraph, newModule.name);
  }

  /// Builds a dependency graph from module registrations.
  ///
  /// Returns a map where keys are module names and values are sets of
  /// module names that the key module depends on.
  static Map<String, Set<String>> _buildDependencyGraph(
    Map<String, ModuleRegistrationModel> moduleRegistrations,
  ) {
    final Map<String, Set<String>> dependencyGraph = <String, Set<String>>{};
    final Map<Type, String> modulesByType = <Type, String>{};

    // Map types to providing modules
    for (final ModuleRegistrationModel registration in moduleRegistrations.values) {
      final List<Type> dependencies = registration.module.dependencies ?? <Type>[];
      for (final Type type in dependencies) {
        modulesByType[type] = registration.module.name;
      }
    }

    // Build the dependency graph
    for (final ModuleRegistrationModel registration in moduleRegistrations.values) {
      final String moduleName = registration.module.name;
      final Set<String> moduleDependencies = <String>{};
      final List<Type> dependencies = registration.module.dependencies ?? <Type>[];

      for (final Type typeDependency in dependencies) {
        final String? provider = modulesByType[typeDependency];
        if (provider != null && provider != moduleName) {
          moduleDependencies.add(provider);
        }
      }

      dependencyGraph[moduleName] = moduleDependencies;
    }

    return dependencyGraph;
  }

  /// Detects cycles in the complete dependency graph.
  static ModuleDependencyValidationResult _detectCycles(
    Map<String, Set<String>> dependencyGraph,
  ) {
    final Set<String> visited = <String>{};
    final Set<String> recursionStack = <String>{};
    final List<List<String>> detectedCycles = <List<String>>[];

    for (final String moduleName in dependencyGraph.keys) {
      final List<String> currentPath = <String>[];
      if (_detectCycleRecursive(
        moduleName,
        dependencyGraph,
        visited,
        recursionStack,
        currentPath,
        detectedCycles,
      )) {
        // Cycle detected - continue checking other modules for additional cycles
      }
    }

    if (detectedCycles.isNotEmpty) {
      final String errorMessage = _buildCycleErrorMessage(detectedCycles);
      return ModuleDependencyValidationResult.failure(
        detectedCycles: detectedCycles,
        errorMessage: errorMessage,
      );
    }

    return const ModuleDependencyValidationResult.success();
  }

  /// Detects cycles that involve a specific module.
  static ModuleDependencyValidationResult _detectCyclesInvolvingModule(
    Map<String, Set<String>> dependencyGraph,
    String targetModule,
  ) {
    final Set<String> visited = <String>{};
    final Set<String> recursionStack = <String>{};
    final List<List<String>> detectedCycles = <List<String>>[];

    // Only check paths that start from or lead to the target module
    final Set<String> modulesToCheck = <String>{targetModule};

    // Add modules that depend on the target module
    for (final MapEntry<String, Set<String>> entry in dependencyGraph.entries) {
      if (entry.value.contains(targetModule)) {
        modulesToCheck.add(entry.key);
      }
    }

    // Add modules that the target module depends on
    modulesToCheck.addAll(dependencyGraph[targetModule] ?? <String>{});

    for (final String moduleName in modulesToCheck) {
      final List<String> currentPath = <String>[];
      if (_detectCycleRecursive(
        moduleName,
        dependencyGraph,
        visited,
        recursionStack,
        currentPath,
        detectedCycles,
      )) {
        // Only include cycles that involve the target module
        final List<List<String>> relevantCycles = detectedCycles
            .where((List<String> cycle) => cycle.contains(targetModule))
            .toList();

        if (relevantCycles.isNotEmpty) {
          final String errorMessage = _buildCycleErrorMessage(relevantCycles);
          return ModuleDependencyValidationResult.failure(
            detectedCycles: relevantCycles,
            errorMessage: errorMessage,
          );
        }
      }
    }

    return const ModuleDependencyValidationResult.success();
  }

  /// Recursive helper method for cycle detection using depth-first search.
  ///
  /// Returns true if a cycle is detected, false otherwise.
  /// Populates [detectedCycles] with any cycles found.
  static bool _detectCycleRecursive(
    String moduleName,
    Map<String, Set<String>> graph,
    Set<String> visited,
    Set<String> recursionStack,
    List<String> currentPath,
    List<List<String>> detectedCycles,
  ) {
    // If not visited, mark as visited and add to recursion stack
    if (!visited.contains(moduleName)) {
      visited.add(moduleName);
      recursionStack.add(moduleName);
      currentPath.add(moduleName);

      // Visit all dependencies
      for (final String dependency in graph[moduleName] ?? <String>{}) {
        // If dependency is in recursion stack, we found a cycle
        if (recursionStack.contains(dependency)) {
          // Build the cycle path
          final int cycleStartIndex = currentPath.indexOf(dependency);
          final List<String> cycle = currentPath.sublist(cycleStartIndex);
          cycle.add(dependency); // Complete the cycle
          detectedCycles.add(cycle);
          return true;
        }

        // If dependency not visited and leads to a cycle
        if (!visited.contains(dependency) &&
            _detectCycleRecursive(
              dependency,
              graph,
              visited,
              recursionStack,
              currentPath,
              detectedCycles,
            )) {
          return true;
        }
      }

      // Remove from current path when backtracking
      currentPath.removeLast();
    }

    // Remove from recursion stack when backtracking
    recursionStack.remove(moduleName);
    return false;
  }

  /// Builds a detailed error message for detected cycles.
  static String _buildCycleErrorMessage(List<List<String>> detectedCycles) {
    if (detectedCycles.isEmpty) {
      return 'No cycles detected';
    }

    final StringBuffer buffer = StringBuffer();
    buffer.writeln('Circular dependency detected in module graph.');

    if (detectedCycles.length == 1) {
      final List<String> cycle = detectedCycles.first;
      final String cycleStr = cycle.join(' → ');
      buffer.write('Dependency cycle: $cycleStr');
    } else {
      buffer.writeln('Multiple dependency cycles detected:');
      for (int i = 0; i < detectedCycles.length; i++) {
        final List<String> cycle = detectedCycles[i];
        final String cycleStr = cycle.join(' → ');
        buffer.writeln('  ${i + 1}. $cycleStr');
      }
    }

    return buffer.toString().trim();
  }

  /// Validates module names for uniqueness across different sources.
  ///
  /// This is a utility method that can be used alongside dependency validation
  /// to ensure module name uniqueness.
  static void validateModuleNames(Map<String, ModuleRegistrationModel> moduleRegistrations) {
    // Group modules by name
    final Map<String, List<ModuleRegistrationModel>> modulesByName = <String, List<ModuleRegistrationModel>>{};

    for (final MapEntry<String, ModuleRegistrationModel> entry in moduleRegistrations.entries) {
      modulesByName.putIfAbsent(entry.key, () => <ModuleRegistrationModel>[]).add(entry.value);
    }

    // Find duplicates
    final List<MapEntry<String, List<ModuleRegistrationModel>>> duplicates = modulesByName.entries
        .where((MapEntry<String, List<ModuleRegistrationModel>> entry) => entry.value.length > 1)
        .toList();

    if (duplicates.isNotEmpty) {
      final String errorMessage = 'Duplicate module names detected:\n${duplicates.map((MapEntry<String, List<ModuleRegistrationModel>> entry) {
        final String modulesInfo = entry.value.map((ModuleRegistrationModel reg) =>
            '${reg.module.runtimeType} from "${reg.source}"').join(', ');
        return '- "${entry.key}" is used by: $modulesInfo';
      }).join('\n')}';

      throw Exception(errorMessage);
    }
  }
}
